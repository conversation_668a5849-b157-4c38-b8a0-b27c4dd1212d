from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import random
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Google Form URL
FORM_URL = "https://docs.google.com/forms/d/e/1FAIpQLSeSj7-zt8exo7lxYzynI1kPltudd6jTL3uZxu7C-ZFFn1rBFw/viewform"
TOTAL_SUBMISSIONS = 10

def setup_driver():
    """Setup Chrome driver with options"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Remove this to see the browser
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    return webdriver.Chrome(options=chrome_options)

def fill_form(driver):
    """Fill the Google Form with random data"""
    try:
        driver.get(FORM_URL)
        wait = WebDriverWait(driver, 10)
        
        # Wait for form to load
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))
        
        # Find and fill text inputs with "nil"
        text_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
        for input_field in text_inputs:
            if input_field.is_displayed():
                input_field.clear()
                input_field.send_keys("nil")
        
        # Handle radio buttons (multiple choice)
        radio_groups = driver.find_elements(By.CSS_SELECTOR, "[role='radiogroup']")
        for group in radio_groups:
            options = group.find_elements(By.CSS_SELECTOR, "[role='radio']")
            if options:
                random.choice(options).click()
        
        # Handle checkboxes - randomly select some
        checkboxes = driver.find_elements(By.CSS_SELECTOR, "[role='checkbox']")
        if checkboxes:
            # Randomly select between 0 and all checkboxes
            num_to_select = random.randint(0, len(checkboxes))
            selected_checkboxes = random.sample(checkboxes, num_to_select)
            for checkbox in selected_checkboxes:
                if checkbox.is_displayed():
                    checkbox.click()
        
        # Handle dropdowns
        selects = driver.find_elements(By.TAG_NAME, "select")
        for select in selects:
            if select.is_displayed():
                select_obj = Select(select)
                options = select_obj.options[1:]  # Skip first option (usually empty)
                if options:
                    select_obj.select_by_index(random.randint(1, len(options)))
        
        # Handle textareas
        textareas = driver.find_elements(By.TAG_NAME, "textarea")
        for textarea in textareas:
            if textarea.is_displayed():
                textarea.clear()
                textarea.send_keys("Sample response text")
        
        # Submit the form
        submit_button = driver.find_element(By.CSS_SELECTOR, "[type='submit']")
        submit_button.click()
        
        # Wait for submission confirmation
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".freebirdFormviewerViewResponseConfirmationMessage")))
        return True
        
    except Exception as e:
        logging.error(f"Error filling form: {e}")
        return False

def main():
    successful = 0
    failed = 0
    
    for i in range(1, TOTAL_SUBMISSIONS + 1):
        logging.info(f"Submitting form #{i}")
        
        driver = setup_driver()
        try:
            if fill_form(driver):
                successful += 1
                logging.info(f"Form #{i} submitted successfully. Total: {successful}")
            else:
                failed += 1
                logging.warning(f"Form #{i} failed. Total failed: {failed}")
        finally:
            driver.quit()
        
        # Delay between submissions
        time.sleep(random.uniform(2, 5))
    
    # Final report
    logging.info("Form submissions completed.")
    logging.info(f"Total: {TOTAL_SUBMISSIONS}, Successful: {successful}, Failed: {failed}")

if __name__ == "__main__":
    main()
